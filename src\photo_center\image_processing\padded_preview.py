"""Padded preview generator for maintaining original dimensions with magenta fill."""

import cv2
import numpy as np
from typing import <PERSON>ple, Optional
from dataclasses import dataclass

from ..utils.logger import get_logger
from .crop_centering import CropCenteringResult


@dataclass
class PaddedPreviewResult:
    """Result of padded preview generation."""
    preview_image: np.ndarray
    original_size: Tuple[int, int]  # (width, height)
    cropped_size: Tuple[int, int]  # (width, height)
    padding_applied: Tuple[int, int, int, int]  # (left, top, right, bottom)
    scale_factor: float
    magenta_color: Tuple[int, int, int]


class PaddedPreviewGenerator:
    """Generator for creating previews with magenta padding to maintain original dimensions."""
    
    def __init__(self, magenta_color: Tuple[int, int, int] = (255, 0, 255)):
        """Initialize padded preview generator.
        
        Args:
            magenta_color: RGB color for padding (default: bright magenta)
        """
        self.magenta_color = magenta_color
        self.logger = get_logger(__name__)
    
    def create_padded_preview(
        self,
        crop_result: CropCenteringResult,
        original_size: Tuple[int, int],
        maintain_aspect: bool = True
    ) -> PaddedPreviewResult:
        """Create a padded preview that maintains original dimensions.

        Args:
            crop_result: Result from crop centering operation
            original_size: Original image size (width, height)
            maintain_aspect: Whether to maintain aspect ratio when scaling

        Returns:
            PaddedPreviewResult with magenta-padded image
        """
        cropped_image = crop_result.cropped_image
        cropped_height, cropped_width = cropped_image.shape[:2]
        target_width, target_height = original_size
        
        self.logger.debug(f"Creating padded preview: cropped={cropped_width}x{cropped_height}, target={target_width}x{target_height}")
        
        # Calculate scaling to fit cropped image within original dimensions
        if maintain_aspect:
            scale_x = target_width / cropped_width
            scale_y = target_height / cropped_height
            scale_factor = min(scale_x, scale_y)
        else:
            scale_factor = 1.0
        
        # Scale the cropped image if needed
        if scale_factor != 1.0:
            new_width = int(cropped_width * scale_factor)
            new_height = int(cropped_height * scale_factor)
            scaled_image = cv2.resize(cropped_image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        else:
            scaled_image = cropped_image.copy()
            new_width, new_height = cropped_width, cropped_height
        
        self.logger.debug(f"Scaled image to: {new_width}x{new_height} (scale_factor={scale_factor:.3f})")
        
        # Create magenta background with target dimensions (match input image dtype)
        input_dtype = cropped_image.dtype
        if input_dtype == np.uint16:
            magenta_color_16bit = tuple(int(c * 256) for c in self.magenta_color)  # Scale to 16-bit
            preview_image = np.full((target_height, target_width, 3), magenta_color_16bit, dtype=np.uint16)
        else:
            preview_image = np.full((target_height, target_width, 3), self.magenta_color, dtype=np.uint8)
        
        # Calculate centering position for scaled image
        center_x = target_width // 2
        center_y = target_height // 2
        
        # Calculate placement coordinates
        start_x = center_x - new_width // 2
        start_y = center_y - new_height // 2
        end_x = start_x + new_width
        end_y = start_y + new_height
        
        # Ensure placement is within bounds
        start_x = max(0, start_x)
        start_y = max(0, start_y)
        end_x = min(target_width, end_x)
        end_y = min(target_height, end_y)
        
        # Calculate actual placement size (in case of clipping)
        actual_width = end_x - start_x
        actual_height = end_y - start_y
        
        # Place the scaled image on the magenta background
        if actual_width > 0 and actual_height > 0:
            # Crop scaled image if it doesn't fit perfectly
            scaled_crop = scaled_image[:actual_height, :actual_width]
            preview_image[start_y:end_y, start_x:end_x] = scaled_crop
        
        # Calculate padding applied
        padding_left = start_x
        padding_top = start_y
        padding_right = target_width - end_x
        padding_bottom = target_height - end_y
        
        self.logger.debug(f"Padding applied: left={padding_left}, top={padding_top}, right={padding_right}, bottom={padding_bottom}")
        
        return PaddedPreviewResult(
            preview_image=preview_image,
            original_size=original_size,
            cropped_size=(cropped_width, cropped_height),
            padding_applied=(padding_left, padding_top, padding_right, padding_bottom),
            scale_factor=scale_factor,
            magenta_color=self.magenta_color
        )
    
    def create_side_by_side_preview(
        self,
        original_image: np.ndarray,
        crop_result: CropCenteringResult,
        padding: int = 10
    ) -> np.ndarray:
        """Create a side-by-side preview showing original and cropped versions.

        Args:
            original_image: Original image
            crop_result: Crop centering result
            padding: Padding between images

        Returns:
            Side-by-side preview image
        """
        orig_height, orig_width = original_image.shape[:2]
        crop_height, crop_width = crop_result.cropped_image.shape[:2]
        
        # Create padded preview for cropped image
        padded_result = self.create_padded_preview(
            crop_result, 
            (orig_width, orig_height), 
            maintain_aspect=True
        )
        
        # Create side-by-side layout
        total_width = orig_width + padded_result.preview_image.shape[1] + padding
        total_height = max(orig_height, padded_result.preview_image.shape[0])
        
        # Create combined image with white background
        combined = np.full((total_height, total_width, 3), (255, 255, 255), dtype=np.uint8)
        
        # Place original image on the left
        combined[:orig_height, :orig_width] = original_image
        
        # Place padded preview on the right
        preview_height, preview_width = padded_result.preview_image.shape[:2]
        start_x = orig_width + padding
        combined[:preview_height, start_x:start_x + preview_width] = padded_result.preview_image
        
        # Add labels
        cv2.putText(combined, "Original", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(combined, "Cropped + Padded", (start_x + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Add crop info
        info_text = f"Crop ratio: {crop_result.crop_ratio:.2f} | Method: {crop_result.method_used}"
        text_size = cv2.getTextSize(info_text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
        text_y = total_height - 20
        
        # Background for info text
        cv2.rectangle(combined, (10, text_y - text_size[1] - 5), 
                     (15 + text_size[0], text_y + 5), (255, 255, 255), -1)
        cv2.putText(combined, info_text, (10, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        return combined
    
    def create_overlay_preview(
        self,
        original_image: np.ndarray,
        crop_result: CropCenteringResult,
        overlay_alpha: float = 0.3
    ) -> np.ndarray:
        """Create an overlay preview showing crop area on original image.

        Args:
            original_image: Original image
            crop_result: Crop centering result
            overlay_alpha: Alpha for overlay effect

        Returns:
            Overlay preview image
        """
        crop_x1, crop_y1, crop_x2, crop_y2 = crop_result.crop_box

        # Create magenta overlay for non-cropped areas (match input image dtype)
        if original_image.dtype == np.uint16:
            magenta_color_16bit = tuple(int(c * 256) for c in self.magenta_color)  # Scale to 16-bit
            magenta_overlay = np.full_like(original_image, magenta_color_16bit, dtype=np.uint16)
        else:
            magenta_overlay = np.full_like(original_image, self.magenta_color, dtype=np.uint8)

        # Blend the overlay with original image
        blended = cv2.addWeighted(original_image, 1 - overlay_alpha, magenta_overlay, overlay_alpha, 0)
        
        # Keep the crop area as original
        blended[crop_y1:crop_y2, crop_x1:crop_x2] = original_image[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Draw crop boundary
        cv2.rectangle(blended, (crop_x1, crop_y1), (crop_x2, crop_y2), (255, 255, 255), 3)
        cv2.rectangle(blended, (crop_x1, crop_y1), (crop_x2, crop_y2), (0, 0, 0), 1)
        
        # Add crop info
        info_text = f"Crop: {crop_x2-crop_x1}x{crop_y2-crop_y1} | Ratio: {crop_result.crop_ratio:.2f}"
        text_size = cv2.getTextSize(info_text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
        
        # Background for info text
        cv2.rectangle(blended, (10, 10), (15 + text_size[0], 15 + text_size[1]), (0, 0, 0), -1)
        cv2.putText(blended, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        return blended
