"""RAW image processing using rawpy."""

import rawpy
import numpy as np
from pathlib import Path
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Union
from PIL import Image, ExifTags
import cv2

from ..utils.logger import get_logger
from ..utils.config import Config


class RawProcessor:
    """RAW image processor using rawpy and imageio."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize RAW processor.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
        
        # Supported RAW extensions
        self.raw_extensions = {'.cr2', '.nef', '.arw', '.dng', '.raw', '.orf', '.rw2', '.pef', '.srw'}
        
    def is_raw_file(self, file_path: Union[str, Path]) -> bool:
        """Check if file is a RAW image file.
        
        Args:
            file_path: Path to image file
            
        Returns:
            True if file is a RAW image
        """
        return Path(file_path).suffix.lower() in self.raw_extensions
    
    def load_image(self, file_path: Union[str, Path]) -> np.ndarray:
        """Load image from file (RAW or standard format).
        
        Args:
            file_path: Path to image file
            
        Returns:
            Image as numpy array in BGR format (for OpenCV compatibility)
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Image file not found: {file_path}")
        
        try:
            if self.is_raw_file(file_path):
                return self._load_raw_image(file_path)
            else:
                return self._load_standard_image(file_path)
                
        except Exception as e:
            self.logger.error(f"Failed to load image {file_path}: {e}")
            raise
    
    def _load_raw_image(self, file_path: Path) -> np.ndarray:
        """Load RAW image using rawpy.

        Args:
            file_path: Path to RAW image file

        Returns:
            Processed image as numpy array in BGR format (16-bit when preserve_bit_depth is True)
        """
        self.logger.info(f"Loading RAW image: {file_path}")

        preserve_bit_depth = self.config.get('image_processing.preserve_bit_depth', True)
        output_bps = 16 if preserve_bit_depth else 8

        # Get RAW processing settings from config
        brightness = self.config.get('image_processing.raw_processing.brightness', 1.0)
        highlight_recovery = self.config.get('image_processing.raw_processing.highlight_recovery', 0)
        exposure_shift = self.config.get('image_processing.raw_processing.exposure_shift', 0.0)
        use_camera_wb = self.config.get('image_processing.raw_processing.use_camera_wb', True)
        gamma_curve = self.config.get('image_processing.raw_processing.gamma_curve', [2.222, 4.5])
        no_auto_bright = self.config.get('image_processing.raw_processing.no_auto_bright', False)

        # Convert highlight recovery setting to HighlightMode enum
        if highlight_recovery == 0:
            highlight_mode = rawpy.HighlightMode.Clip
        elif highlight_recovery == 1:
            highlight_mode = rawpy.HighlightMode.Ignore
        elif highlight_recovery == 2:
            highlight_mode = rawpy.HighlightMode.Blend
        elif 3 <= highlight_recovery <= 9:
            highlight_mode = rawpy.HighlightMode.Reconstruct(highlight_recovery)
        else:
            # Default to Clip for invalid values
            highlight_mode = rawpy.HighlightMode.Clip
            self.logger.warning(f"Invalid highlight_recovery value {highlight_recovery}, using Clip mode")

        with rawpy.imread(str(file_path)) as raw:
            # Post-process RAW image with configurable settings
            rgb_image = raw.postprocess(
                use_camera_wb=use_camera_wb,  # Use camera white balance (configurable)
                half_size=False,              # Full resolution
                no_auto_bright=no_auto_bright, # Configurable auto brightness (default: disabled)
                output_bps=output_bps,        # 16-bit or 8-bit based on config
                gamma=tuple(gamma_curve),     # Gamma curve from config
                bright=brightness,            # Brightness multiplier from config
                highlight_mode=highlight_mode, # Highlight mode from config (corrected parameter name)
                exp_shift=exposure_shift,     # Exposure shift from config
                exp_preserve_highlights=0.0,  # Preserve highlights
                use_auto_wb=False,            # Don't override camera WB
                user_flip=0,                  # No rotation
                demosaic_algorithm=rawpy.DemosaicAlgorithm.AHD  # High quality demosaicing
            )

        # Convert from RGB to BGR for OpenCV
        bgr_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)

        # Only convert to 8-bit if preserve_bit_depth is False or if explicitly requested
        if not preserve_bit_depth and bgr_image.dtype == np.uint16:
            bgr_image = self._convert_16bit_to_8bit(bgr_image)

        self.logger.debug(f"RAW image loaded: shape={bgr_image.shape}, dtype={bgr_image.dtype}")
        return bgr_image
    
    def _load_standard_image(self, file_path: Path) -> np.ndarray:
        """Load standard image format using OpenCV.

        Args:
            file_path: Path to image file

        Returns:
            Image as numpy array in BGR format (preserves original bit depth when possible)
        """
        self.logger.debug(f"Loading standard image: {file_path}")

        # Try to load with original bit depth first (for TIFF, PNG with 16-bit support)
        preserve_bit_depth = self.config.get('image_processing.preserve_bit_depth', True)

        if preserve_bit_depth:
            # Try loading with cv2.IMREAD_UNCHANGED to preserve bit depth
            image = cv2.imread(str(file_path), cv2.IMREAD_UNCHANGED)
            if image is not None:
                # Convert grayscale to BGR if needed
                if len(image.shape) == 2:
                    image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                elif len(image.shape) == 3 and image.shape[2] == 4:
                    # Convert BGRA to BGR
                    image = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)

                self.logger.debug(f"Standard image loaded with original bit depth: dtype={image.dtype}")
                # Apply EXIF orientation correction
                image = self._apply_exif_orientation(image, file_path)
                return image

        # Fallback to standard 8-bit loading
        image = cv2.imread(str(file_path))
        if image is None:
            raise ValueError(f"Could not load image: {file_path}")

        # Apply EXIF orientation correction to prevent unwanted rotation
        image = self._apply_exif_orientation(image, file_path)

        return image

    def _apply_exif_orientation(self, image: np.ndarray, file_path: Path) -> np.ndarray:
        """Apply EXIF orientation correction to prevent unwanted rotation.

        Args:
            image: Input image
            file_path: Path to image file for EXIF reading

        Returns:
            Image with correct orientation
        """
        try:
            # Read EXIF data using PIL
            with Image.open(file_path) as pil_img:
                exif = pil_img._getexif()

                if exif is not None:
                    # Find orientation tag
                    orientation_key = None
                    for tag, value in ExifTags.TAGS.items():
                        if value == 'Orientation':
                            orientation_key = tag
                            break

                    if orientation_key and orientation_key in exif:
                        orientation = exif[orientation_key]

                        # Apply rotation based on EXIF orientation
                        # Only apply if orientation indicates rotation is needed
                        if orientation == 3:
                            # 180 degrees
                            image = cv2.rotate(image, cv2.ROTATE_180)
                            self.logger.debug("Applied 180° rotation from EXIF")
                        elif orientation == 6:
                            # 90 degrees clockwise
                            image = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
                            self.logger.debug("Applied 90° clockwise rotation from EXIF")
                        elif orientation == 8:
                            # 90 degrees counter-clockwise
                            image = cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
                            self.logger.debug("Applied 90° counter-clockwise rotation from EXIF")
                        # Orientation 1 = normal, no rotation needed
                        # Other orientations (2,4,5,7) involve flipping which we'll skip for now

        except Exception as e:
            # If EXIF reading fails, just continue without rotation
            self.logger.debug(f"Could not read EXIF orientation for {file_path}: {e}")

        return image

    def save_image(
        self,
        image: np.ndarray,
        output_path: Union[str, Path],
        quality: Optional[int] = None,
        format_override: Optional[str] = None
    ) -> None:
        """Save image to file with appropriate bit depth handling.

        Args:
            image: Image as numpy array in BGR format (8-bit or 16-bit)
            output_path: Output file path
            quality: JPEG quality (1-100). If None, uses config default
            format_override: Override output format. If None, uses config default
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Determine output format
        output_format = format_override or self.config.output_format
        quality = quality or self.config.output_quality
        force_8bit_for_jpeg = self.config.get('image_processing.force_8bit_for_jpeg', True)

        # Prepare image for saving based on format and bit depth
        save_image = image.copy()

        try:
            if output_format.lower() in ['jpg', 'jpeg']:
                # JPEG doesn't support 16-bit, convert to 8-bit if needed
                if save_image.dtype == np.uint16 and force_8bit_for_jpeg:
                    save_image = self._convert_16bit_to_8bit(save_image)
                    self.logger.debug("Converted 16-bit to 8-bit for JPEG output")

                cv2.imwrite(
                    str(output_path.with_suffix('.jpg')),
                    save_image,
                    [cv2.IMWRITE_JPEG_QUALITY, quality]
                )
            elif output_format.lower() == 'png':
                # PNG supports both 8-bit and 16-bit
                if save_image.dtype == np.uint16:
                    self.logger.debug("Saving 16-bit PNG")
                cv2.imwrite(str(output_path.with_suffix('.png')), save_image)
            elif output_format.lower() in ['tiff', 'tif']:
                # TIFF supports both 8-bit and 16-bit
                if save_image.dtype == np.uint16:
                    self.logger.debug("Saving 16-bit TIFF")
                cv2.imwrite(str(output_path.with_suffix('.tiff')), save_image)
            else:
                # Default to JPEG with 8-bit conversion if needed
                if save_image.dtype == np.uint16:
                    save_image = self._convert_16bit_to_8bit(save_image)
                    self.logger.debug("Converted 16-bit to 8-bit for default JPEG output")

                cv2.imwrite(
                    str(output_path.with_suffix('.jpg')),
                    save_image,
                    [cv2.IMWRITE_JPEG_QUALITY, quality]
                )

            self.logger.info(f"Image saved: {output_path} (dtype: {save_image.dtype})")

        except Exception as e:
            self.logger.error(f"Failed to save image {output_path}: {e}")
            raise
    
    def resize_image(
        self, 
        image: np.ndarray, 
        max_size: Optional[Tuple[int, int]] = None,
        maintain_aspect: bool = True
    ) -> np.ndarray:
        """Resize image while maintaining aspect ratio.
        
        Args:
            image: Input image
            max_size: Maximum size as (width, height). If None, uses config default
            maintain_aspect: Whether to maintain aspect ratio
            
        Returns:
            Resized image
        """
        if max_size is None:
            max_size = self.config.get('image_processing.max_output_size')
            if max_size is None:
                return image  # No resizing
        
        height, width = image.shape[:2]
        max_width, max_height = max_size
        
        if width <= max_width and height <= max_height:
            return image  # No resizing needed
        
        if maintain_aspect:
            # Calculate scaling factor
            scale = min(max_width / width, max_height / height)
            new_width = int(width * scale)
            new_height = int(height * scale)
        else:
            new_width, new_height = max_width, max_height
        
        resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        self.logger.debug(f"Image resized from {width}x{height} to {new_width}x{new_height}")
        
        return resized

    def prepare_for_inference(self, image: np.ndarray) -> np.ndarray:
        """Prepare image for model inference (convert to 8-bit if needed).

        Args:
            image: Input image (8-bit or 16-bit)

        Returns:
            Image suitable for model inference (always 8-bit)
        """
        if image.dtype == np.uint16:
            # Convert 16-bit to 8-bit with proper tone mapping
            inference_image = self._convert_16bit_to_8bit(image)
            self.logger.debug("Converted 16-bit to 8-bit for model inference")
            return inference_image
        else:
            # Already 8-bit, return as-is
            return image

    def prepare_for_display(self, image: np.ndarray) -> np.ndarray:
        """Prepare image for GUI display (convert to 8-bit if needed).

        Args:
            image: Input image (8-bit or 16-bit)

        Returns:
            Image suitable for display (always 8-bit)
        """
        if image.dtype == np.uint16:
            # Convert 16-bit to 8-bit with proper tone mapping for display
            display_image = self._convert_16bit_to_8bit(image)
            self.logger.debug("Converted 16-bit to 8-bit for display")
            return display_image
        else:
            # Already 8-bit, return as-is
            return image

    def _convert_16bit_to_8bit(self, image_16bit: np.ndarray) -> np.ndarray:
        """Convert 16-bit image to 8-bit with proper tone mapping.

        Args:
            image_16bit: 16-bit input image

        Returns:
            8-bit image with proper tone mapping
        """
        # Method 1: Percentile-based scaling (more robust for RAW images)
        # Find the 1st and 99th percentiles to avoid extreme values
        p1 = np.percentile(image_16bit, 1)
        p99 = np.percentile(image_16bit, 99)

        # Clip and scale to 0-255 range
        clipped = np.clip(image_16bit, p1, p99)
        scaled = ((clipped - p1) / (p99 - p1) * 255).astype(np.uint8)

        return scaled

    def get_bit_depth(self, image: np.ndarray) -> int:
        """Get the bit depth of an image.

        Args:
            image: Input image

        Returns:
            Bit depth (8 or 16)
        """
        if image.dtype == np.uint16:
            return 16
        elif image.dtype == np.uint8:
            return 8
        else:
            self.logger.warning(f"Unknown image dtype: {image.dtype}, assuming 8-bit")
            return 8
    
    def get_image_info(self, file_path: Union[str, Path]) -> dict:
        """Get image information without loading the full image.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Dictionary with image information
        """
        file_path = Path(file_path)
        
        info = {
            'path': str(file_path),
            'exists': file_path.exists(),
            'size_bytes': file_path.stat().st_size if file_path.exists() else 0,
            'is_raw': self.is_raw_file(file_path),
            'extension': file_path.suffix.lower()
        }
        
        try:
            if self.is_raw_file(file_path):
                # Get RAW image info
                with rawpy.imread(str(file_path)) as raw:
                    info.update({
                        'width': raw.sizes.width,
                        'height': raw.sizes.height,
                        'camera_make': getattr(raw, 'camera_make', 'Unknown'),
                        'camera_model': getattr(raw, 'camera_model', 'Unknown'),
                    })
            else:
                # Get standard image info using PIL
                with Image.open(file_path) as img:
                    info.update({
                        'width': img.width,
                        'height': img.height,
                        'mode': img.mode,
                        'format': img.format
                    })
        
        except Exception as e:
            self.logger.warning(f"Could not get image info for {file_path}: {e}")
            info['error'] = str(e)
        
        return info
