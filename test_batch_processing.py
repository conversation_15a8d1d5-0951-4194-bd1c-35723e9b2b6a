#!/usr/bin/env python3
"""Test script for batch processing with new crop centering and logging."""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.photo_center.batch.batch_processor import BatchProcessor
from src.photo_center.utils.config import Config

def test_batch_processing():
    """Test batch processing with a single image."""
    
    # Setup paths
    test_input_dir = Path("test_photos")
    test_output_dir = Path("test_output_batch")
    
    if not test_input_dir.exists():
        print(f"Test directory {test_input_dir} not found!")
        return False
    
    # Clean up previous test output
    if test_output_dir.exists():
        import shutil
        shutil.rmtree(test_output_dir)
    
    print("Testing batch processing with crop centering...")
    print(f"Input directory: {test_input_dir}")
    print(f"Output directory: {test_output_dir}")
    
    try:
        # Initialize batch processor
        config = Config()
        processor = BatchProcessor(config)
        
        # Process directory
        result = processor.process_directory(
            str(test_input_dir),
            str(test_output_dir),
            recursive=False
        )
        
        print(f"\nBatch processing completed!")
        print(f"Total files: {result.total_files}")
        print(f"Processed: {result.processed_files}")
        print(f"Failed: {result.failed_files}")
        print(f"Skipped: {result.skipped_files}")
        print(f"Processing time: {result.processing_time:.2f}s")
        
        # Check if log file was created
        logs_dir = Path("./logs")
        if logs_dir.exists():
            log_files = list(logs_dir.glob("cropped_photos_*.log"))
            if log_files:
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                print(f"\nLog file created: {latest_log}")
                
                # Show first few lines of log
                with open(latest_log, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:20]  # First 20 lines
                    print("\nLog file preview:")
                    print("=" * 50)
                    for line in lines:
                        print(line.rstrip())
                    if len(lines) == 20:
                        print("... (truncated)")
                    print("=" * 50)
        
        # Check output files
        if test_output_dir.exists():
            output_files = list(test_output_dir.rglob("*"))
            output_images = [f for f in output_files if f.is_file() and f.suffix.lower() in ['.jpg', '.png', '.tiff', '.tif']]
            print(f"\nOutput images created: {len(output_images)}")
            for img in output_images:
                print(f"  - {img}")
        
        return result.processed_files > 0
        
    except Exception as e:
        print(f"Error during batch processing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_batch_processing()
    if success:
        print("\n✓ Batch processing test completed successfully!")
    else:
        print("\n✗ Batch processing test failed!")
        sys.exit(1)
